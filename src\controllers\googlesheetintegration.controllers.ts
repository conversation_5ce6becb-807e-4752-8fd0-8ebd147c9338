import dotenv from "dotenv";
dotenv.config({ path: "./.env" });
import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { google } from "googleapis";
import { getValidGoogleToken } from "../utils/GoogleToken";
import { mergeFieldsWithAnswers } from "../utils/comman";
const BATCH_SIZE = 1000;

const CLIENT_ID = process.env.CLIENT_ID;
const CLIENT_SECRET = process.env.CLIENT_SECRET;
const REDIRECT_URI = process.env.REDIRECT_URI;

const oauth2Client = new google.auth.OAuth2(
  CLIENT_ID,
  CLIENT_SECRET,
  REDIRECT_URI
);
type FormField = {
  id: any;
  name: any;
  type: any;
  component: any;
  value: any;
  [key: string]: any; // Index Signature for dynamic properties
};
const getActionforGoogleSheet = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const integrationId = c.req.param("id");
    if (!integrationId) {
      return sendApiError(c, "Integration ID is missing", 400);
    }

    // Fetch actions related to the given integration ID
    const { data: actions, error } = await supabase
      .from("automate_form_integration_actions")
      .select("id, name, description, created_at")
      .eq("integration_id", integrationId);

    if (error) {
      console.error("Error fetching integration actions:", error);
      return sendApiError(c, "Failed to fetch actions", 500);
    }

    return sendApiResponse(c, {
      message: "Integration actions retrieved successfully",
      data: actions,
    });
  } catch (err) {
    console.error("Error fetching integration actions:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const getUserIntegrationCredentials = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const integrationId = c.req.param("id");
    if (!integrationId) {
      return sendApiError(c, "Integration ID is missing", 400);
    }

    // Fetch user credentials for the given integration
    const { data: credentials, error } = await supabase
      .from("automate_form_integration_credentials")
      .select("id, auth_type, enabled, created_at,name")
      .eq("integration_id", integrationId)
      .eq("created_by", user.user.id);

    if (error || !credentials) {
      return sendApiError(c, "No credentials found for this integration", 404);
    }

    return sendApiResponse(c, {
      message: "Integration credentials retrieved successfully",
      data: credentials || [],
    });
  } catch (err) {
    console.error("Error fetching user integration credentials:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
// const addUserConnection = async (c: Context) => {
//   try {
//     // const user = c.get("user");
//     // if (!user) {
//     //   return sendApiError(c, "Unauthorized", 401);
//     // }

//     const userId = 'f0b27bf4-d1be-4106-8840-0bf0b78d1ec7'
//     const { redirect_uri, integration_id, formId, formType, name } =
//       c.req.query();
//     if (!integration_id || !redirect_uri || !name || !formId || !formType) {
//       return sendApiError(c, "Missing required parameters", 400);
//     }

//     // Validate integration
//     const { data: integration, error: integrationError } = await supabase
//       .from("integrations")
//       .select("name")
//       .eq("id", integration_id)
//       .single();

//     if (integrationError || !integration) {
//       console.error("Integration Error:", integrationError);
//       return sendApiError(c, "Failed to retrieve integration", 500);
//     }

//     // Generate authentication URL if not authenticated
//     const authUrl = oauth2Client.generateAuthUrl({
//       access_type: "offline",
//       prompt: "consent",
//       scope: [
//         "https://www.googleapis.com/auth/spreadsheets",
//         "https://www.googleapis.com/auth/drive.readonly",
//         "https://www.googleapis.com/auth/userinfo.email",
//       ],
//       state: JSON.stringify({
//         userId,
//         redirect_uri,
//         integration_id,
//         formId,
//         formType,
//         name,
//       }),
//     });
//     return c.redirect(authUrl);
//   } catch (err) {
//     console.error("Google Auth Error:", err);
//     return sendApiError(c, "Internal server error", 500);
//   }
// };
const getUserSheets = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
   
    const { credentialId} =
    await c.req.json();

    if (!credentialId) {
      return sendApiError(c, "Missing credential ID in header", 400);
    }

    // Get the valid Google token
    const googleAccessToken = await getValidGoogleToken(credentialId);

    if (!googleAccessToken) {
      return sendApiError(c, "User has not authenticated Google", 403);
    }

    // Initialize Google API client
    const oauth2Client = new google.auth.OAuth2();
    oauth2Client.setCredentials({ access_token: googleAccessToken });
    const drive = google.drive({ version: "v3", auth: oauth2Client });

    // Fetch all Google Sheets
    const response = await drive.files.list({
      q: "mimeType='application/vnd.google-apps.spreadsheet'",
      fields: "files(id, name, webViewLink, createdTime)",
      pageSize: 100,
    });

    const sheets = response.data.files || [];

    return sendApiResponse(c, {
      message: "User sheets retrieved successfully",
      data: sheets,
    });
  } catch (err) {
    console.error(" Error fetching user sheets:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const linkFormToGoogleSheet = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { formId, spreadsheetId, credentialId, actionId } =
      await c.req.json();
    if (!formId || !spreadsheetId || !credentialId || !actionId) {
      return sendApiError(
        c,
        "formId, spreadsheetId, credentialId, and actionId are required",
        400
      );
    }

    const google_accestoken = await getValidGoogleToken(credentialId);

    if (!google_accestoken) {
      return sendApiError(c, "User has not authenticated Google", 403);
    }

    const oauth2Client = new google.auth.OAuth2();
    oauth2Client.setCredentials({ access_token: google_accestoken });
    const sheets = google.sheets({ version: "v4", auth: oauth2Client });

    const { data: integrationData, error: integrationError } = await supabase
      .from("automate_form_integration_actions")
      .select("integration_id")
      .eq("id", actionId)
      .single();

    if (integrationError) {
      console.error("Error in retrieving integration:", integrationError);
      return sendApiError(c, "Error in retrieving integration", 500);
    }

    const { error: formIntegrationError } = await supabase
      .from("form_integrations")
      .insert({
        form_id: formId,
        integration_id: integrationData.integration_id,
        credential_id: credentialId,
        action_id: actionId,
        metadata: { spreadsheetId },
        created_by: user.user.id,
        enabled: true
      });

    if (formIntegrationError) {
      console.error("Error inserting form integration:", formIntegrationError);
      return sendApiError(c, "Failed integration", 500);
    }

    // Retrieve past form responses
    const { data: pastResponses, error: responseError } = await supabase
      .from("automate_form_responses")
      .select("*")
      .eq("form_id", formId);

    if (responseError) {
      console.error("Failed to fetch past responses:", responseError.message);
      return sendApiError(c, "Failed to fetch past responses", 500);
    }
    
    console.log("pastResponses", pastResponses);
    
    if (pastResponses.length > 0) {
      const sheetResponse = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range: "Sheet1!A1:Z1",
      });

      let existingHeaders = sheetResponse.data.values?.[0] || [];
      console.log("📑 Existing Headers:", existingHeaders);

      let newHeaders = [...existingHeaders];
      let valuesToInsert: any[] = [];
      
      pastResponses.forEach((record) => {
        let rowData: any = {};

        // Process each answer in the response
        record.answers.forEach((answer: any) => {
          if (typeof answer.value === "object" && answer.value !== null) {
            // Handle name fields with firstName and lastName
            if ('firstName' in answer.value && 'lastName' in answer.value) {
              // First Name
              const firstNameTitle = answer.firstNameTitle || "First Name";
              if (!newHeaders.includes(firstNameTitle)) {
                newHeaders.push(firstNameTitle);
              }
              rowData[firstNameTitle] = answer.value.firstName;
              
              // Last Name
              const lastNameTitle = answer.lastNameTitle || "Last Name";
              if (!newHeaders.includes(lastNameTitle)) {
                newHeaders.push(lastNameTitle);
              }
              rowData[lastNameTitle] = answer.value.lastName;
            } else {
              // Handle other object values
              Object.entries(answer.value).forEach(([key, value]) => {
                const columnTitle = answer[`${key}Title`] || key;
                if (!newHeaders.includes(columnTitle)) {
                  newHeaders.push(columnTitle);
                }
                rowData[columnTitle] = value;
              });
            }
          } else {
            // Use title as the header if available, otherwise use name
            const headerName = answer.title || answer.name;
            if (!newHeaders.includes(headerName)) {
              newHeaders.push(headerName);
            }
            rowData[headerName] = answer.value;
          }
        });

        // Add Submitted At column
        if (!newHeaders.includes("Submitted At")) {
          newHeaders.push("Submitted At");
        }
        rowData["Submitted At"] = record.submitted_at || new Date().toISOString();

        // Convert row data to array format for Google Sheets
        valuesToInsert.push(newHeaders.map((header) => rowData[header] || ""));
      });

      console.log("🆕 Updated Headers:", newHeaders);

      // Update the headers if new columns are needed
      if (newHeaders.length > existingHeaders.length) {
        await sheets.spreadsheets.values.update({
          spreadsheetId,
          range: `Sheet1!A1:${String.fromCharCode(65 + newHeaders.length - 1)}1`,
          valueInputOption: "RAW",
          requestBody: { values: [newHeaders] },
        });
        console.log("✅ Updated Headers in Google Sheets");
      }

      // Insert past responses into the sheet
      if (valuesToInsert.length > 0) {
        await sheets.spreadsheets.values.append({
          spreadsheetId,
          range: `Sheet1!A2:${String.fromCharCode(65 + newHeaders.length - 1)}${valuesToInsert.length + 1}`,
          valueInputOption: "RAW",
          requestBody: { values: valuesToInsert },
        });
        console.log("✅ Inserted Past Form Responses into Google Sheet");
      }
    }

    return sendApiResponse(
      c,
      {
        message:
          "Form linked to Google Sheets successfully and past responses added",
        sheetUrl: `https://docs.google.com/spreadsheets/d/${spreadsheetId}`,
      },
      200
    );
  } catch (err) {
    console.error("Error linking form to Google Sheet:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const createGoogleSheet = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const { formId, credentialId, actionId } =
    await c.req.json();
    if (!formId  || !credentialId || !actionId) {
      return sendApiError(
        c,
        "all fileds are required",
        400
      );
    }
    const google_accestoken = await getValidGoogleToken(credentialId);

    if (!google_accestoken) {
      return sendApiError(c, "User not authenticated with Google", 401);
    }
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("title")
      .eq("id", formId)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    const { data: integrationData, error: integrationError } = await supabase
      .from("automate_form_integration_actions")
      .select("integration_id")
      .eq("id", actionId)
      .single();

    if (integrationError) {
      console.error(" Error in retriving integration:", integrationError);
      return sendApiError(c, "Error in retrinving integration", 500);
    }

    const sheetName = `[AutomateForm.ai] ${form.title} Responses`;

    const oauth2Client = new google.auth.OAuth2();
    oauth2Client.setCredentials({ access_token: google_accestoken });
    const sheets = google.sheets({ version: "v4", auth: oauth2Client });

    console.log("🆕 Creating new Google Sheet...");
    const response = await sheets.spreadsheets.create({
      requestBody: { properties: { title: sheetName } },
    });

    const newSheetId = response.data.spreadsheetId;
    if (!newSheetId) throw new Error("Failed to create Google Sheet");

    console.log("✅ New sheet created:", newSheetId);

    const { error: formIntegrationError } = await supabase
    .from("form_integrations")
    .insert({
      form_id: formId,
      integration_id: integrationData.integration_id,
      credential_id: credentialId,
      action_id: actionId,
      metadata: { spreadsheetId: newSheetId },
      created_by: user.user.id,
      enabled: true
    });

    if (formIntegrationError) {
      console.error("Error inserting form integration:", formIntegrationError);
      return sendApiError(c, "failed integration", 500);
    }
  
    // Fetch past responses
    const { data: pastResponses, error: responseError } = await supabase
      .from("automate_form_responses")
      .select("*")
      .eq("form_id", formId);

    if (responseError) {
      return sendApiError(c, "Failed to fetch past responses", 500);
    }

    // Process past responses if any exist
    if (pastResponses.length > 0) {
      const sheetResponse = await sheets.spreadsheets.values.get({
        spreadsheetId: newSheetId,
        range: "Sheet1!A1:Z1",
      });

      let existingHeaders = sheetResponse.data.values?.[0] || [];
      console.log("📑 Existing Headers:", existingHeaders);

      let newHeaders = [...existingHeaders];
      let valuesToInsert: any[] = [];

      pastResponses.forEach((record) => {
        let rowData: any = {};

        // Process each answer in the response
        record.answers.forEach((answer: any) => {
          if (typeof answer.value === "object" && answer.value !== null) {
            // Handle name fields with firstName and lastName
            if ('firstName' in answer.value && 'lastName' in answer.value) {
              // First Name
              const firstNameTitle = answer.firstNameTitle || "First Name";
              if (!newHeaders.includes(firstNameTitle)) {
                newHeaders.push(firstNameTitle);
              }
              rowData[firstNameTitle] = answer.value.firstName;
              
              // Last Name
              const lastNameTitle = answer.lastNameTitle || "Last Name";
              if (!newHeaders.includes(lastNameTitle)) {
                newHeaders.push(lastNameTitle);
              }
              rowData[lastNameTitle] = answer.value.lastName;
            } else {
              // Handle other object values
              Object.entries(answer.value).forEach(([key, value]) => {
                const columnTitle = answer[`${key}Title`] || key;
                if (!newHeaders.includes(columnTitle)) {
                  newHeaders.push(columnTitle);
                }
                rowData[columnTitle] = value;
              });
            }
          } else {
            // Use title as the header if available, otherwise use name
            const headerName = answer.title || answer.name;
            if (!newHeaders.includes(headerName)) {
              newHeaders.push(headerName);
            }
            rowData[headerName] = answer.value;
          }
        });

        // Add Submitted At column
        if (!newHeaders.includes("Submitted At")) {
          newHeaders.push("Submitted At");
        }
        rowData["Submitted At"] = record.submitted_at || new Date().toISOString();

        // Convert row data to array format for Google Sheets
        valuesToInsert.push(newHeaders.map((header) => rowData[header] || ""));
      });

      console.log("🆕 Updated Headers:", newHeaders);

      // Update sheet headers if new columns exist
      if (newHeaders.length > existingHeaders.length) {
        await sheets.spreadsheets.values.update({
          spreadsheetId: newSheetId,
          range: `Sheet1!A1:${String.fromCharCode(65 + newHeaders.length - 1)}1`,
          valueInputOption: "RAW",
          requestBody: { values: [newHeaders] },
        });
        console.log("✅ Updated Headers in Google Sheets");
      }

      // Insert past responses into the sheet
      if (valuesToInsert.length > 0) {
        await sheets.spreadsheets.values.append({
          spreadsheetId: newSheetId,
          range: `Sheet1!A2:${String.fromCharCode(65 + newHeaders.length - 1)}${valuesToInsert.length + 1}`,
          valueInputOption: "RAW",
          requestBody: { values: valuesToInsert },
        });
        console.log("✅ Inserted Past Form Responses into Google Sheet");
      }
    }

    return sendApiResponse(c, {
      message: "Google Sheet created and linked successfully",
      spreadsheetId: newSheetId,
      sheetUrl: `https://docs.google.com/spreadsheets/d/${newSheetId}`,
    });
  } catch (error) {
    console.error("Error creating Google Sheet:", error);
    return sendApiError(c, "Failed to create Google Sheet", 500);
  }
};


export {
  getActionforGoogleSheet,
  linkFormToGoogleSheet,
  createGoogleSheet,
  getUserSheets,
  getUserIntegrationCredentials
};
